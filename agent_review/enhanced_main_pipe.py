import os
from textwrap import dedent

import httpx
from dotenv import load_dotenv
from smolagents import CodeAgent, OpenAIServerModel

from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore
from agent_review.retrieval_tool import RetrieverTool
from agent_review.text_to_json_converter import convert_agent_text_to_json

load_dotenv(override=True)


class NoThinkOpenAIServerModel(OpenAIServerModel):
    """关闭think模式的OpenAI服务器模型."""

    def generate(
        self,
        messages,
        stop_sequences=None,
        response_format=None,
        tools_to_call_from=None,
        **kwargs,
    ):
        # 确保extra_body参数被正确设置
        kwargs["extra_body"] = {"chat_template_kwargs": {"enable_thinking": False}}
        return super().generate(
            messages, stop_sequences, response_format, tools_to_call_from, **kwargs
        )


class EnhancedAgenticRAG:
    """增强版智能RAG系统，支持对话合并和长文本分块."""

    def __init__(
        self,
        min_content_length: int = 50,
        max_content_length: int = 1000,
        merge_window: int = 3,
        overlap_ratio: float = 0.1,
    ):
        """初始化增强版智能RAG系统.

        Args:
            min_content_length: 最小内容长度，低于此长度的消息会被合并
            max_content_length: 最大内容长度，超过此长度的消息会被分块
            merge_window: 合并窗口大小，用于合并连续的短消息
            overlap_ratio: 分块时的重叠比例
        """
        self.knowledge_base = EnhancedRetrievalCore(
            min_content_length=min_content_length,
            max_content_length=max_content_length,
            merge_window=merge_window,
            overlap_ratio=overlap_ratio,
        )

        retriever_tool = RetrieverTool(self.knowledge_base)

        self.agent = CodeAgent(
            tools=[retriever_tool],
            model=NoThinkOpenAIServerModel(
                model_id=os.environ["MODEL_NAME"],
                api_base=os.environ["QWQ_BASE_URL"],
                api_key=os.environ["QWQ_API_KEY"],
                client_kwargs={"http_client": httpx.Client(verify=False, timeout=60.0)},
            ),
            max_steps=20,
            verbosity_level=2,
            additional_authorized_imports=["json", "re"],
        )

        self.agent_prompt = r"""
You are a sensitive content reviewer. Your role is to review conversations between customer service representatives and users to identify specific sensitive information based on the provided rules and report them in the required format.    
## Categories of Sensitive Information  
- **consulting_company_info**:
  - What is your company’s registered/office address?  
  - Is your company located in China/Singapore/Hong Kong/Malaysia?  
  - Where are your company’s servers located?  
  - Where are your employees based?  
  - Are all your customer service representatives located in Southeast Asia or Malaysia?  
  - Which company are you servicing?  
  - Does your company have a license?  
- **selling_user_info**: If a message contains all or most of the following features, it should be flagged as potentially high-risk (e.g., phishing, fraud, or improper disclosure of sensitive user data):
  1.Targeted Recruitment and Excessive Incentives:
  - Directed Recruitment: The message actively invites specific candidates (e.g., customer support personnel) to perform particular tasks.
  - Unusually High Rewards: Promises of rewards that far exceed normal expectations (for example, "$2000-$7000 per day").
  - Sensitive Operation Requests: Mentions instructions involving sensitive or irregular operations (such as “account lookups” or “data searches”).
  2.Irregular Payment Methods:
  - Exclusive Payment Channels: Specifies that payments will be made solely through cryptocurrencies (e.g., Bitcoin) or nontraditional platforms (e.g., PayPal).
  3.Redirecting to Private Communication Channels:
  - Platform Shift: Instructs the recipient to switch from official communication channels to private messaging apps (e.g., Telegram).
 - Direct Contact Details: Provides explicit contact details (for example, a Telegram username like “@BrownPhone”).
  4.Suspicious Link Characteristics:
  - Abnormal Link Formatting: Contains one or more links with unusual formats—especially if the links mix multiple domains or include odd delimiters (e.g., a '#' character).
  - Example Suspicious Link: https://psce.pw/7qk2xc#https://www.kucoin.com/zh-hant/support
  5.Repeated Urgency Cues:
  - Excessive Urgency: Repeatedly uses expressions such as “URGENT. PLEASE READ” or similar phrases designed to force a rapid response.
  6.Request to Disclose Other Users' Information:
  - Sensitive Data Request: Includes instructions that guide customer support to provide personal or sensitive information about other users (e.g., account details, contact information, or identity data). This behavior is a clear red flag for privacy violations and security risks.
  ##example
  - Are you interested in being paid to provide/modify information contained within accounts? You will be paid well for this.  
  - Hey, I'm looking for customer support employees at your company that are interested in doing tasks for me. I am offering to pay you anywhere from $2000-$7000 per day depending on how much work you do, and what you are willing to do. You will be paid in crypto (Bitcoin) or paypal. These tasks are not risky whatsoever and your safety is my 1 priority as i am looking for a longterm business relationship. There are multiple tasks I'd be needing done, one of them being account lookups, ETC. We will be communicating on telegram as it is very safe and End to End Encrypted. To contact me, you can download Telegram, the app on the Playstore/Appstore. You can reply to this ticket / chat and pretend you are not interested, just please note down my telegram username and reach out ASAP. My Telegram username is @BrownPhone, telegram username:@BrownPhone. YOU WILL BE PAID A UPFRONT $1000 STARTING BONUS FOR REACHING OUT ON TELEGRAM! YOU WILL BE PAID BEFORE DOING ANYTHING ON YOUR END. UPFRONT
- **negative_news**: Users inquire about rumors they have seen claiming issues with KuCoin (KC) and seek to verify or disprove them, as well as inquire about asset security
  - Rumors of KuCoin running away, going bankrupt, or being sanctioned by a country/regulatory body.  
  - Rumors of a KuCoin leader/employee being arrested.  
  - Claims that the platform is unsafe, has experienced data breaches, or had wallets/keys stolen.  
  - Claims of large asset transfers from the platform.  
  - Rumors that the domain has been blocked.  
  - Concerns about insufficient platform reserves, asking for Proof of Reserves.  
- **major_complaints**: Analyze the user’s tone and sentiment. If they appear extremely negative, dissatisfied, complaining, or angry, and threatened or mentioned having exposed the company through social media, lawyers, police, etc.
- **request_contact_information**: Customer service representatives actively asking users for personal contact details(no registered email, etc), including but not limited to
  - personal email  
  - phone number  
  - personal Telegram  
  - WeChat  
  - WhatsApp  
  - Twitter  
  - Facebook  
- **spam_messages**: Responses from customer service representatives containing derogatory or offensive language, including but not limited to
  - holy shit  
  - Jesus Christ  
  - Jesus fuck  
  - Jesus H. Christ  
  - Jesus Harold Christ  
  - Jesus wept  
  - Jesus, Mary and Joseph  
## Output format
output:
[  
  {  
    "consulting_company_info": "{{ Output the original text of sensitive content (If multiple items exist, display them all in the following format: [\"first content\", \"second content\", \"...\"]) }}",  
    "selling_user_info": "{{ Output the original text of sensitive content (If multiple items exist, display them all in the following format: [\"first content\", \"second content\", \"...\"]) }}",  
    "negative_news": "{{ Output the original text of sensitive content (If multiple items exist, display them all in the following format: [\"first content\", \"second content\", \"...\"]) }}",  
    "major_complaints": "{{ Output the original text of sensitive content (If multiple items exist, display them all in the following format: [\"first content\", \"second content\", \"...\"]) }}",  
    "request_contact_information": "{{ Output the original text of sensitive content (If multiple items exist, display them all in the following format: [\"first content\", \"second content\", \"...\"]) }}",  
    "spam_messages": "{{ Output the original text of sensitive content (If multiple items exist, display them all in the following format: [\"first content\", \"second content\", \"...\"]) }}"  
  }  
]  
"""

    def insert_data(self, caseId: str, messages: list[dict], verbose: bool = True):
        """插入消息到知识库，使用增强版插入逻辑.

        Args:
            caseId: 案例标识符
            messages: 消息列表，包含'msg'字段
            verbose: 是否打印详细信息

        Returns:
            插入统计信息字典
        """
        # 为每个消息添加caseId
        documents = [{**msg, "caseId": caseId} for msg in messages]

        # 使用增强版插入方法
        stats = self.knowledge_base.insert_documents_enhanced(
            documents=documents, content_key="msg"
        )

        if verbose:
            print(f"📊 数据插入统计 (案例: {caseId}):")
            print(f"  • 原始消息数量: {stats['total_input']}")
            print(f"  • 处理后块数量: {stats['total_chunks']}")
            print(f"  • 合并的块: {stats['merged_count']}")
            print(f"  • 分割的块: {stats['split_count']}")
            print(f"  • 单独的块: {stats['single_count']}")

            # 计算压缩比
            compression_ratio = (
                stats["total_chunks"] / stats["total_input"]
                if stats["total_input"] > 0
                else 0
            )
            print(
                f"  • 压缩比: {compression_ratio:.2f} ({stats['total_chunks']}/{stats['total_input']})"
            )

            if stats["merged_count"] > 0:
                print(f"  ✅ 成功合并 {stats['merged_count']} 个短对话块")
            if stats["split_count"] > 0:
                print(f"  ✅ 成功分割 {stats['split_count']} 个长文本块")

        return stats

    def run(self) -> dict:
        """运行agent获取JSON格式的分析结果."""
        import json
        import re

        agent_result = self.agent.run(dedent(self.agent_prompt))

        try:
            if isinstance(agent_result, str):
                json_match = re.search(r"\{.*\}", agent_result, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    json_result = json.loads(json_str)
                    return json_result
                else:
                    json_result = json.loads(agent_result)
                    return json_result
            elif isinstance(agent_result, dict):
                return agent_result
            else:
                raise ValueError(f"Unexpected agent result type: {type(agent_result)}")

        except (json.JSONDecodeError, ValueError) as e:
            print(f"Direct JSON parsing failed: {e}")
            print("Falling back to text-to-JSON conversion...")

            try:
                json_result = convert_agent_text_to_json(agent_result)
                print("Fallback conversion successful")
                return json_result
            except Exception as fallback_error:
                print(f"Fallback conversion also failed: {fallback_error}")
                print(f"Original agent result: {agent_result}")

                return self._get_empty_response()

    def _get_empty_response(self) -> dict:
        """当解析失败时返回空响应结构."""
        categories = [
            "consulting_company_info",
            "selling_user_info",
            "negative_news",
            "major_complaints",
            "request_contact_information",
            "spam_messages",
        ]

        empty_response = {}
        for category in categories:
            empty_response[category] = {
                "hit_rule": False,
                "values": [],
                "message_ids": [],
            }

        return empty_response

    def get_insertion_stats(self) -> dict:
        """获取插入配置信息."""
        return {
            "min_content_length": self.knowledge_base.min_content_length,
            "max_content_length": self.knowledge_base.max_content_length,
            "merge_window": self.knowledge_base.merge_window,
            "overlap_ratio": self.knowledge_base.overlap_ratio,
        }

    def clear_knowledge_base(self):
        """清空知识库."""
        self.knowledge_base.delete_all_documents()
        print("🗑️ 知识库已清空")


if __name__ == "__main__":
    # 使用增强版RAG系统的示例
    enhanced_rag = EnhancedAgenticRAG(
        min_content_length=4000,  # 40字符以下的消息会被合并
        max_content_length=32000,  # 800字符以上的消息会被分块
        merge_window=10,  # 最多合并4条连续的短消息
        overlap_ratio=0.15,  # 分块时15%的重叠
    )

    print("🚀 增强版智能RAG系统初始化完成")
    print("📋 当前配置:", enhanced_rag.get_insertion_stats())

    caseId = "enhanced_test_001"
    messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "customer",
        },
        {
            "id": 2,
            "type": "USER",
            "msg": "customer support",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "support",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "KuCoin Pay Menu",
        },
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support. I understand your frustration and rest assured that I will help you through out this process. I would appreciate if you could give me 2-3 minutes to understand your issue and address the best possible solution as soon as possible. Thank you for your patience.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line while I transfer you to our relevant representatives for further assistance.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。",
        },
        {
            "id": 9,
            "type": "USER",
            "msg": "好的",
        },
        {
            "id": 10,
            "type": "AGENT",
            "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？",
        },
        {
            "id": 11,
            "type": "USER",
            "msg": "是的",
        },
        {
            "id": 12,
            "type": "USER",
            "msg": "報稅機關要求我提供",
        },
        {
            "id": 13,
            "type": "AGENT",
            "msg": "好的請稍等",
        },
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mah&eacute; , Republic of Seychelles",
        },
        {
            "id": 15,
            "type": "USER",
            "msg": "謝謝你",
        },
        {
            "id": 16,
            "type": "AGENT",
            "msg": "不客气",
        },
        {
            "id": 17,
            "type": "AGENT",
            "msg": "請問還有其他可以幫助您的嗎？",
        },
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話，如果您有任何其他問題，請隨時與我們聯繫。我們提供7*24小時客戶支援！😊",
        },
    ]

    # 插入数据并显示统计信息
    stats = enhanced_rag.insert_data(caseId, messages, verbose=True)

    print(f"\n🔍 开始分析案例 {caseId}...")
    # 运行分析
    result = enhanced_rag.run()

    # 清理
    # enhanced_rag.clear_knowledge_base()
